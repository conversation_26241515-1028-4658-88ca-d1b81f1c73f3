import sqlite3
import json
import os
from pathlib import Path
from video_retrieval import video_config

def normalize_video_path(path: str) -> str:
    """
    Convert relative paths to absolute paths and standardize format.

    Args:
        path: The path to normalize

    Returns:
        Normalized absolute path as string
    """
    if path is None:
        return None
    return str(os.path.abspath(os.path.expanduser(str(path))))

# Ensure DB_DIR exists (config.py should handle this, but defensive check)
# The ensure_directories in video_config is now more robust and handles DB_DIR.
# if not os.path.exists(video_config.DB_DIR):
# os.makedirs(video_config.DB_DIR, exist_ok=True)

def get_db_connection():
    """Establishes a connection to the SQLite database."""
    # video_config.ensure_directories() # Ensure DB path is valid before connecting
    conn = sqlite3.connect(video_config.METADATA_DB_PATH)
    conn.row_factory = sqlite3.Row # Access columns by name
    # Enable Foreign Key support if not enabled by default (good practice)
    conn.execute("PRAGMA foreign_keys = ON")
    return conn

def create_tables():
    """Creates the necessary tables in the database if they don\'t exist."""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Enable WAL mode for better concurrency, as suggested in user's reference
    cursor.execute("PRAGMA journal_mode=WAL;")

    # Check if we need to add the visual_boundaries and semantic_boundaries columns
    cursor.execute("PRAGMA table_info(long_videos)")
    columns = cursor.fetchall()
    column_names = [col[1] for col in columns]

    # Add missing columns if they don't exist
    if 'visual_boundaries' not in column_names:
        print("Adding visual_boundaries column to long_videos table...")
        cursor.execute("ALTER TABLE long_videos ADD COLUMN visual_boundaries TEXT;")

    if 'semantic_boundaries' not in column_names:
        print("Adding semantic_boundaries column to long_videos table...")
        cursor.execute("ALTER TABLE long_videos ADD COLUMN semantic_boundaries TEXT;")

    # Table for original long videos
    # Renamed 'tags' to 'description', added 'keywords'
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS long_videos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            video_path TEXT NOT NULL,
            source TEXT,
            source_id TEXT,
            description TEXT,               -- Renamed from tags; For sentence-level descriptions
            keywords TEXT,                  -- For space-separated atomic keywords/tags
            raw_api_metadata TEXT,
            full_transcript_text TEXT,
            duration_seconds REAL,
            width INTEGER,
            height INTEGER,
            visual_boundaries TEXT,         -- JSON string of visual scene boundaries
            semantic_boundaries TEXT,       -- JSON string of semantic topic boundaries
            processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT uq_source_id UNIQUE (source, source_id)
        )
    ''')
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_long_videos_video_path ON long_videos (video_path);")

    # Table for video clips
    # Renamed 'tags' to 'description', added 'keywords'
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS video_clips (
            clip_id INTEGER PRIMARY KEY AUTOINCREMENT,
            clip_path TEXT UNIQUE NOT NULL,
            original_long_video_id INTEGER,
            start_time_in_original REAL,
            end_time_in_original REAL,
            duration_seconds REAL,
            transcript_text TEXT,
            description TEXT,               -- Renamed from tags; Inherited description
            keywords TEXT,                  -- Inherited keywords
            visual_embedding_path TEXT,
            text_embedding_path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (original_long_video_id) REFERENCES long_videos (id) ON DELETE CASCADE
        )
    ''')
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_video_clips_original_long_video_id ON video_clips (original_long_video_id);")

    # FTS5 table - now targeting the new 'keywords' field from long_videos
    cursor.execute('''
        CREATE VIRTUAL TABLE IF NOT EXISTS video_keywords_fts USING fts5(
            video_id UNINDEXED,  -- Corresponds to long_videos.id
            keywords,            -- Indexing the new keywords column
            content='long_videos',
            content_rowid='id',
            tokenize = "porter unicode61" -- Retaining example tokenizer
        );
    ''')
    print("FTS table video_keywords_fts (for long_videos.keywords) created/ensured.")

    cursor.execute('''
        CREATE TABLE IF NOT EXISTS pyscenedetect_params (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            param_set_name TEXT UNIQUE NOT NULL,
            content_threshold REAL,
            content_min_scene_len INTEGER,
            adaptive_threshold REAL,
            adaptive_min_scene_len INTEGER,
            threshold_detector_threshold REAL,
            threshold_detector_min_scene_len INTEGER,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    conn.commit()
    conn.close()
    print("Database tables ensured (with WAL mode, new fields, and FTS table placeholder).")

# --- Long Video Operations ---
def add_long_video(video_path: str,
                   source: str | None = None,
                   source_id: str | None = None,
                   description: str | None = None, # Renamed from tags
                   keywords: list[str] | None = None, # New field for keywords
                   raw_api_metadata: dict | None = None,
                   full_transcript_text: str | None = None,
                   duration_seconds: float | None = None,
                   width: int | None = None,
                   height: int | None = None) -> int | None:
    # Normalize the path to absolute path
    video_path = normalize_video_path(video_path)

    conn = get_db_connection()
    cursor = conn.cursor()
    # Process keywords list into a space-separated string
    keywords_str = " ".join(sorted(list(set(k.strip().lower() for k in keywords if k.strip())))) if keywords else ""
    raw_api_json = json.dumps(raw_api_metadata) if raw_api_metadata else "{}"

    try:
        cursor.execute('''
            INSERT INTO long_videos
            (video_path, source, source_id, description, keywords, raw_api_metadata, full_transcript_text, duration_seconds, width, height)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (video_path, source, source_id, description, keywords_str, raw_api_json, full_transcript_text, duration_seconds, width, height))
        conn.commit()
        last_id = cursor.lastrowid
        return last_id
    except sqlite3.IntegrityError as e:
        # This can happen due to UNIQUE constraint on (video_path) or (source, source_id)
        print(f"Integrity error adding long video (Path: {video_path}, Source: {source}, SourceID: {source_id}): {e}")
        # Retrieve existing ID based on source and source_id if they are the cause of conflict
        if source and source_id:
            cursor.execute("SELECT id FROM long_videos WHERE source = ? AND source_id = ?", (source, source_id))
            row = cursor.fetchone()
            if row:
                print(f"Found existing DB ID by source/source_id: {row['id']}")
                # Optionally, update other fields if this is an intended update path
                # For now, just return the existing ID. The `downloader_common.save_video_metadata`
                # has more sophisticated update logic if called.
                return row['id']
        # Fallback to check by video_path if source/source_id check didn't yield result
        cursor.execute("SELECT id FROM long_videos WHERE video_path = ?", (video_path,))
        row = cursor.fetchone()
        if row:
             print(f"Found existing DB ID by video_path: {row['id']}")
             return row['id']
        return None # Should not happen if IntegrityError was raised for one of the constraints
    finally:
        conn.close()

def get_long_video_info(video_id: int | None = None, video_path: str | None = None, source: str | None = None, source_id: str | None = None) -> dict | None:
    """Fetches a long video's info by its DB ID, path, or source/source_id."""
    if not any([video_id, video_path, (source and source_id)]):
        print("Error: Must provide video_id, video_path, or both source and source_id to get_long_video_info.")
        return None

    # Normalize the path if provided
    if video_path is not None:
        video_path = normalize_video_path(video_path)

    conn = get_db_connection()
    cursor = conn.cursor()
    query = "SELECT * FROM long_videos WHERE "
    params = []

    if video_id is not None:
        query += "id = ?"
        params.append(video_id)
    elif video_path is not None:
        query += "video_path = ?"
        params.append(video_path)
    elif source and source_id:
        query += "source = ? AND source_id = ?"
        params.extend([source, source_id])
    else: # Should be caught by initial check
        conn.close()
        return None

    cursor.execute(query, tuple(params))
    row = cursor.fetchone()
    conn.close()
    return dict(row) if row else None


def get_long_video_id(video_path: str | None = None, source: str | None = None, source_id: str | None = None) -> int | None:
    info = get_long_video_info(video_path=video_path, source=source, source_id=source_id)
    return info['id'] if info else None

def get_long_video_by_path(video_path: str) -> dict | None:
    """
    Fetches a long video's info by its path.
    This is a convenience wrapper around get_long_video_info.

    Args:
        video_path: The path of the video to fetch.

    Returns:
        A dictionary with the video's info, or None if not found.
    """
    return get_long_video_info(video_path=video_path)

def update_long_video_metadata(video_id: int,
                              description: str | None = None,
                              keywords_json_str: str | None = None,
                              raw_api_metadata_json: str | None = None,
                              transcript_text: str | None = None,
                              duration_sec: float | None = None,
                              width: int | None = None,
                              height: int | None = None,
                              visual_boundaries_json: str | None = None,
                              semantic_boundaries_json: str | None = None) -> bool:
    """
    Updates metadata for a long video in the database.

    Args:
        video_id: The ID of the long video to update.
        description: Optional description text to update.
        keywords_json_str: Optional JSON string of keywords to update.
        raw_api_metadata_json: Optional JSON string of raw API metadata to update.
        transcript_text: Optional transcript text to update.
        duration_sec: Optional duration in seconds to update.
        width: Optional width to update.
        height: Optional height to update.
        visual_boundaries_json: Optional JSON string of visual boundaries to update.
        semantic_boundaries_json: Optional JSON string of semantic boundaries to update.

    Returns:
        True if the update was successful, False otherwise.
    """
    if not video_id:
        print("Error: video_id is required for update_long_video_metadata.")
        return False

    conn = get_db_connection()
    cursor = conn.cursor()

    # Build the update query dynamically based on provided parameters
    update_fields = []
    params = []

    if description is not None:
        update_fields.append("description = ?")
        params.append(description)

    if keywords_json_str is not None:
        update_fields.append("keywords = ?")
        params.append(keywords_json_str)

    if raw_api_metadata_json is not None:
        update_fields.append("raw_api_metadata = ?")
        params.append(raw_api_metadata_json)

    if transcript_text is not None:
        update_fields.append("full_transcript_text = ?")
        params.append(transcript_text)

    if duration_sec is not None:
        update_fields.append("duration_seconds = ?")
        params.append(duration_sec)

    if width is not None:
        update_fields.append("width = ?")
        params.append(width)

    if height is not None:
        update_fields.append("height = ?")
        params.append(height)

    if visual_boundaries_json is not None:
        update_fields.append("visual_boundaries = ?")
        params.append(visual_boundaries_json)

    if semantic_boundaries_json is not None:
        update_fields.append("semantic_boundaries = ?")
        params.append(semantic_boundaries_json)

    # If no fields to update, return early
    if not update_fields:
        print(f"No fields provided to update for video_id {video_id}.")
        conn.close()
        return False

    # Construct and execute the update query
    query = f"UPDATE long_videos SET {', '.join(update_fields)} WHERE id = ?"
    params.append(video_id)

    try:
        cursor.execute(query, tuple(params))
        conn.commit()
        print(f"Successfully updated metadata for video_id {video_id}.")
        return True
    except Exception as e:
        print(f"Error updating metadata for video_id {video_id}: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

# --- Video Clip Operations ---
def add_video_clip(clip_path: str,
                   duration_seconds: float,
                   original_long_video_id: int | None = None,
                   start_time_in_original: float | None = None,
                   end_time_in_original: float | None = None,
                   transcript_text: str | None = None,
                   description: str | None = None, # Renamed from tags
                   keywords: list[str] | str | None = None, # New field, can be list or space-separated string
                   visual_embedding_path: str | None = None,
                   text_embedding_path: str | None = None) -> int | None:
    # Normalize the path to absolute path
    clip_path = normalize_video_path(clip_path)

    # Normalize embedding paths if provided
    if visual_embedding_path:
        visual_embedding_path = normalize_video_path(visual_embedding_path)
    if text_embedding_path:
        text_embedding_path = normalize_video_path(text_embedding_path)

    conn = get_db_connection()
    cursor = conn.cursor()

    if isinstance(keywords, list):
        keywords_str = " ".join(sorted(list(set(k.strip().lower() for k in keywords if k.strip())))) if keywords else ""
    elif isinstance(keywords, str):
        keywords_str = keywords # Assume already processed if string
    else:
        keywords_str = ""

    # Ensure description is a string, even if None is passed
    description_str = description if description is not None else ""

    try:
        cursor.execute('''
            INSERT INTO video_clips (
                clip_path, original_long_video_id, start_time_in_original,
                end_time_in_original, duration_seconds, transcript_text, description, keywords,
                visual_embedding_path, text_embedding_path
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (clip_path, original_long_video_id, start_time_in_original,
              end_time_in_original, duration_seconds, transcript_text, description_str, keywords_str,
              visual_embedding_path, text_embedding_path))
        conn.commit()
        return cursor.lastrowid
    except sqlite3.IntegrityError:
        print(f"Video clip {clip_path} already exists in DB.")
        cursor.execute("SELECT clip_id FROM video_clips WHERE clip_path = ?", (clip_path,))
        row = cursor.fetchone()
        return row['clip_id'] if row else None
    finally:
        conn.close()

def get_clip_info(clip_id: int) -> dict | None:
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM video_clips WHERE clip_id = ?", (clip_id,))
    row = cursor.fetchone()
    conn.close()
    return dict(row) if row else None

def get_all_clips_for_indexing(force=False):
    """
    Fetches clips that need indexing/re-indexing.
    Now includes 'description' and 'keywords' from the video_clips table.
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    # Select necessary fields including new description and keywords
    fields_to_select = "vc.clip_id, vc.clip_path, vc.transcript_text, vc.description AS clip_description, vc.keywords AS clip_keywords, lv.description AS original_video_description, lv.keywords AS original_video_keywords"

    if force:
        print("Force mode enabled: Re-indexing ALL clips, including those with existing embeddings.")
        query = f"""
            SELECT {fields_to_select}
            FROM video_clips vc
            LEFT JOIN long_videos lv ON vc.original_long_video_id = lv.id
        """
        cursor.execute(query)
    else:
        query = f"""
            SELECT {fields_to_select}
            FROM video_clips vc
            LEFT JOIN long_videos lv ON vc.original_long_video_id = lv.id
            WHERE vc.visual_embedding_path IS NULL OR vc.text_embedding_path IS NULL
        """
        cursor.execute(query)

    rows = cursor.fetchall()
    conn.close()

    results = []
    for row_proxy in rows:
        row = dict(row_proxy)
        # Combine description and keywords, preferring clip-specific if available
        final_description = row.get('clip_description')
        if not final_description:
            final_description = row.get('original_video_description', "")

        final_keywords = row.get('clip_keywords')
        if not final_keywords:
            final_keywords = row.get('original_video_keywords', "")

        results.append({
            'clip_id': row['clip_id'],
            'clip_path': row['clip_path'],
            'transcript_text': row['transcript_text'],
            'description': final_description if final_description else "",
            'keywords': final_keywords if final_keywords else ""
        })
    return results


def update_clip_embeddings(clip_id, visual_embedding_path, text_embedding_path):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE video_clips
        SET visual_embedding_path = ?, text_embedding_path = ?
        WHERE clip_id = ?
    ''', (visual_embedding_path, text_embedding_path, clip_id))
    conn.commit()
    conn.close()

def update_video_clip_transcript(clip_id, transcript_text):
    """
    Updates the transcript text for a video clip.

    Args:
        clip_id: The ID of the clip to update.
        transcript_text: The new transcript text.

    Returns:
        True if the update was successful, False otherwise.
    """
    if not clip_id:
        print("Error: clip_id is required for update_video_clip_transcript.")
        return False

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            UPDATE video_clips
            SET transcript_text = ?
            WHERE clip_id = ?
        ''', (transcript_text, clip_id))
        conn.commit()
        return True
    except Exception as e:
        print(f"Error updating transcript for clip_id {clip_id}: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def get_clip_metadata_for_retrieval(clip_ids):
    """Retrieves metadata for a list of clip_ids, including description and keywords."""
    if not clip_ids:
        return []

    # Debug: Print the clip_ids we're looking for
    print(f"DEBUG DB: Looking for clip_ids: {clip_ids}")

    conn = get_db_connection()
    cursor = conn.cursor()

    # First, check if these clip_ids exist in the database
    for clip_id in clip_ids:
        cursor.execute("SELECT COUNT(*) FROM video_clips WHERE clip_id = ?", (clip_id,))
        count = cursor.fetchone()[0]
        print(f"DEBUG DB: clip_id {clip_id} exists in DB: {count > 0}")

    # Use a simpler query to get results
    placeholders = ','.join(str(clip_id) for clip_id in clip_ids)
    query = f"""
        SELECT clip_id, clip_path, transcript_text, description, keywords
        FROM video_clips
        WHERE clip_id IN ({placeholders})
    """
    print(f"DEBUG DB: Simple query: {query}")
    cursor.execute(query)
    rows = cursor.fetchall()
    print(f"DEBUG DB: Simple query results count: {len(rows)}")

    # Get additional metadata for each clip
    result = []
    for row in rows:
        row_dict = dict(row)
        clip_id = row_dict['clip_id']

        # Get original video metadata
        cursor.execute("""
            SELECT lv.video_path as original_video_path, lv.source as original_source,
                   lv.source_id as original_source_id, lv.description as original_video_description,
                   lv.keywords as original_video_keywords
            FROM video_clips vc
            LEFT JOIN long_videos lv ON vc.original_long_video_id = lv.id
            WHERE vc.clip_id = ?
        """, (clip_id,))
        original_video_row = cursor.fetchone()

        if original_video_row:
            original_video_dict = dict(original_video_row)
            row_dict.update(original_video_dict)

        # Process keywords if they're in JSON format
        if row_dict.get('keywords') and row_dict['keywords'].strip().startswith('[') and row_dict['keywords'].strip().endswith(']'):
            try:
                import json
                keywords_list = json.loads(row_dict['keywords'])
                if isinstance(keywords_list, list):
                    # Convert list to space-separated string for display
                    row_dict['keywords_display'] = " ".join(str(kw).strip() for kw in keywords_list if str(kw).strip())
                    print(f"Processed JSON keywords for clip_id {row_dict['clip_id']}: {row_dict['keywords_display'][:100]}...")
            except json.JSONDecodeError:
                # If JSON parsing fails, use as is
                row_dict['keywords_display'] = row_dict['keywords']
        else:
            # Not JSON format, use as is
            row_dict['keywords_display'] = row_dict.get('keywords', '')

        # Add retrieval score placeholder (will be filled in later)
        row_dict['retrieval_score'] = 0.0

        result.append(row_dict)

    conn.close()
    return result

# --- Tuned Params Operations (Example) --- Unchanged
def save_tuned_pyscenedetect_params(name, params, notes=""):
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute("INSERT INTO pyscenedetect_params (param_set_name, content_threshold, content_min_scene_len, adaptive_threshold, adaptive_min_scene_len, threshold_detector_threshold, threshold_detector_min_scene_len, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                       (name,
                        params.get('content_detector',{}).get('threshold'),
                        params.get('content_detector',{}).get('min_scene_len'),
                        params.get('adaptive_detector',{}).get('adaptive_threshold'),
                        params.get('adaptive_detector',{}).get('min_scene_len'),
                        params.get('threshold_detector',{}).get('threshold'),
                        params.get('threshold_detector',{}).get('min_scene_len'),
                        notes)
                      )
        conn.commit()
        return cursor.lastrowid
    except sqlite3.IntegrityError:
        print(f"Parameter set {name} already exists.")
        return None
    finally:
        conn.close()

def load_tuned_pyscenedetect_params(name):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM pyscenedetect_params WHERE param_set_name = ?", (name,))
    row = cursor.fetchone()
    conn.close()
    if row:
        return {
            "content_detector": {
                "threshold": row["content_threshold"],
                "min_scene_len": row["content_min_scene_len"]
            },
            "adaptive_detector": {
                "adaptive_threshold": row["adaptive_threshold"],
                "min_scene_len": row["adaptive_min_scene_len"]
            },
            "threshold_detector": {
                "threshold": row["threshold_detector_threshold"],
                "min_scene_len": row["threshold_detector_min_scene_len"]
                # Add fade_bias here if it were in the DB schema for this table
            }
        }
    return None

# Initialize database and tables on first import
# create_tables() # This is good for ensuring tables exist when the module is loaded.
# Consider calling this explicitly in your main application startup instead if preferred.
# For now, let's keep it here as it was.
if __name__ == '__main__':
    print("Initializing database and tables directly from metadata_db.py...")
    video_config.ensure_directories() # Make sure directories are there first
    create_tables()
    print("Database setup complete.")

    # Example: Add a long video
    # test_long_video_id = add_long_video(
    #     video_path="/test/dummy_long_video.mp4",
    #     source="manual_test",
    #     source_id="dummy_long_001",
    #     description="A test video for DB schema. This is a sentence.",
    #     keywords=["test", "dummy", "long video"],
    #     raw_api_metadata={"info": "Some test info"},
    #     duration_seconds=300.5,
    #     width=1920,
    #     height=1080
    # )
    # print(f"Test long video ID: {test_long_video_id}")

    # if test_long_video_id:
    #     # Example: Add a clip for this long video
    #     test_clip_id = add_video_clip(
    #         clip_path=f"/test/dummy_clip_for_{test_long_video_id}.mp4",
    #         original_long_video_id=test_long_video_id,
    #         start_time_in_original=0.0,
    #         end_time_in_original=60.0,
    #         duration_seconds=60.0,
    #         transcript_text="This is a test transcript for the dummy clip.",
    #         description="Inherited or specific clip description.",
    #         keywords=["test_clip", "segment1"]
    #     )
    #     print(f"Test clip ID: {test_clip_id}")

    #     # Example: Get all clips for indexing (force=True to see the new clip)
    #     clips_for_indexing = get_all_clips_for_indexing(force=True)
    #     print("\nClips for indexing (force=True):")
    #     for clip_data in clips_for_indexing:
    #         if clip_data['clip_id'] == test_clip_id:
    #             print(clip_data)
    #             break

    #     retrieved_clip_info = get_clip_metadata_for_retrieval([test_clip_id])
    #     print("\nRetrieved clip info for search:")
    #     print(retrieved_clip_info)