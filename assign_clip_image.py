#!/usr/bin/env python3
# assign_clip_image.py - Enhanced media assignment using BGE, BM25, Cross-Encoder, and CLIP post-filter

import os
import sys
import numpy as np
import json
import argparse
import logging
import copy
import math
import re
from typing import List, Dict, Any, Tu<PERSON>, Optional
from collections import defaultdict
from PIL import Image
import cv2
import torch
import warnings
import shutil
import io
import hashlib
from functools import lru_cache
from botocore.exceptions import ClientError


# BGE for text embeddings and reranking
from sentence_transformers import SentenceTransformer, CrossEncoder
# CLIP for visual post-filtering
from sentence_transformers import SentenceTransformer as ClipSentenceTransformer
# BLIP-2 for object-rich captions (still used for initial description generation)
from transformers import Blip2Processor, Blip2ForConditionalGeneration
# BM25 for sparse retrieval
from rank_bm25 import BM25Okapi
# For text processing in BM25
import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
# AWS for object detection (still used for initial tag generation)
import boto3

# FAISS for dense retrieval
import faiss

# Import LLM interface for summarization
from modules.langchain_interface import call_llm_json_response
# Import modules from utils
from modules.utils import resize_large_image

# Import new modularized code
from modules.similarity_assignment import (
    preprocess, create_safe_zero_vector,
    get_clip_model_for_filter, get_bge_bi_encoder, get_bge_reranker,
    encode_image_clip_for_filter, encode_text_clip_for_filter, encode_text_bge,
    build_bge_index_and_data, calculate_bge_similarity_and_rerank,
    apply_clip_post_filter, enhanced_ilp_optimization
)

from modules.metadata_enrichment import (
    _to_jpeg_bytes, get_blip_models, generate_blip_caption, 
    _rekognition_cached, extract_tags_rekognition,
    summarize_image_text_with_llm, enrich_image_metadata
)

# Import from existing modules
from config import logger, VECTOR_STORE_DIR, get_image_metadata_path, FILTERED_IMAGE_DIR
from modules.image_assignment import (update_attribute_json, get_valid_images, print_assignment_status, 
                                     load_clips_data, assign_clips) # Removed ilp_optimization, use enhanced_ilp_optimization

# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning, module='torch.amp.autocast_mode')
warnings.filterwarnings("ignore", category=FutureWarning)

# Check for MPS (Metal Performance Shaders) availability and CUDA
if torch.cuda.is_available():
    device = "cuda"
elif hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
    device = "mps"
else:
    device = "cpu"
print(f"Using primary device: {device}")


# Constants
NULL_REWARD = 0.2 # Keep for potential future use, but ILP handles null internally
REPEAT_PENALTY = 1 # Keep for ILP
MIN_DISTANCE_BETWEEN_REUSE = 3 # Default reuse distance for first round ILP
MAX_TOTAL_USES = 3 # Default max uses for fallback ILP calculation
MIN_WORDS = 20 # Keep for potential future use
MAX_WORDS = 40 # Keep for potential future use
TOP_K_RETRIEVAL = 20  # Number of candidates to retrieve using BGE bi-encoder
TOP_N_POST_FILTER = 5  # Number of top candidates to check with CLIP visual filter (from similarity_assignment)
MAX_DIVERSITY_PAIRS_PER_SEGMENT = 5000 # Keep for ILP diversity constraint
BGE_EMBEDDING_DIM = 768  # Dimension for BGE-base-en-v1.5
CLIP_EMBEDDING_DIM = 512  # Dimension for CLIP ViT-B/32 (used for post-filter)
# **: 绝对质量阈值：高相似度候选必须 ≥ 该分数
RERANKER_SCORE_MIN_THRESHOLD = 0.1 
# Round 3 candidate control
TOP_M_FOR_ROUND3 = 10  # Max number of BGE candidates to consider per paragraph
BGE_SIMILARITY_THRESHOLD_ROUND3 = 0.2 # Min BGE cosine similarity in Round 3 (adjustable via CLI)
SECONDS_PER_IMAGE = 8

# Download nltk data if needed
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    logger.info("NLTK 'punkt' tokenizer not found. Attempting download...")
    try:
        nltk.download('punkt')
        logger.info("NLTK 'punkt' downloaded successfully.")
    except Exception as e:
        logger.warning(f"Failed to download NLTK 'punkt' tokenizer: {e}")
        logger.warning("Sentence tokenization might be less accurate. Proceeding anyway.")

# Add global flag for dry run mode
DRY_RUN = False


# 添加计算需要图片数量的函数
def calculate_num_images_needed(duration: float) -> int:
    """根据音频持续时间计算需要的图片数量
    
    Args:
        duration: 音频持续时间（秒） 
        
    Returns:
        int: 需要的图片数量，公式为 ceil(duration / SECONDS_PER_IMAGE) 
    """
    return math.ceil(duration / SECONDS_PER_IMAGE)

# --- Round 3: Assign remaining existing images ---
def assign_remaining_images(
    paragraphs: List[Dict],
    filtered_metadata: List[Dict],
    image_bge_embeddings: Optional[np.ndarray],
    device: str,
    max_usage_per_image: int = 1,
    min_distance_between_reuse: int = 2
) -> None:
    """
    Round 3 assignment: use all remaining unassigned images (ignore TOP_K_RETRIEVAL),
    assign to paragraphs not yet assigned after clip assignment.
    Single-image paragraphs via ILP, multi-image greedily.
    """
    global args
    # load Round 3 parameters from CLI or use defaults
    top_m_r3 = getattr(args, "top_m_round3", TOP_M_FOR_ROUND3)
    r3_thr = getattr(args, "round3_threshold", BGE_SIMILARITY_THRESHOLD_ROUND3)
    logger.info("Starting Round 3: Assign remaining existing images.")
    # 1. Identify unassigned paragraphs using the same calculation as Round 4 placeholders
    unassigned_single = []
    unassigned_multi = []
    for idx, para in enumerate(paragraphs):
        # Skip anchors entirely
        if para.get('assigned_media', {}).get('type') == 'anchor':
            continue

        # Determine how many images have already been assigned
        assigned_info = para.get('assigned_media', {})
        assigned_type = assigned_info.get('type')
        existing_paths = []
        if assigned_type == 'image' and assigned_info.get('filepath'):
            existing_paths = [assigned_info['filepath']]
        elif assigned_type in ['multi_image', 'multi_image_pending']:
            existing_paths = list(assigned_info.get('filepaths', []))

        # Compute remaining needed images exactly as in Round 4
        total_needed = para.get('num_images_needed', 1)
        remaining_to_assign = total_needed - len(existing_paths)
        if remaining_to_assign <= 0:
            continue
        if remaining_to_assign == 1:
            unassigned_single.append(idx)
        else:
            unassigned_multi.append(idx)

    if not unassigned_single and not unassigned_multi:
        logger.info("Round 3: No paragraphs require assignment in this round.")
        return
    logger.info(f"Round 3: Identified {len(unassigned_single)} single-assign and {len(unassigned_multi)} multi-assign paragraphs for this round.")

    # 2. Collect used image paths from previous rounds
    used_image_filepaths = set()
    for para in paragraphs:
        media_info = para.get('assigned_media', {})
        media_type = media_info.get('type')
        if media_type == 'image' and media_info.get('filepath'):
            used_image_filepaths.add(media_info['filepath'])
        elif media_type == 'multi_image': # Fully assigned multi_image from R1
            used_image_filepaths.update(media_info.get('filepaths', []))
        # Not adding from 'multi_image_pending' filepaths here, as we want to complete them if possible.

    logger.info(f"Round 3: Found {len(used_image_filepaths)} images used in prior assignments (R1 Images, R2 Clips don't add to this set).")

    # 3. Build list of candidate image meta_indices (all *unused* images from filtered_metadata with embeddings)
    candidate_meta_indices = []
    if image_bge_embeddings is not None:
        for i, meta in enumerate(filtered_metadata):
            if meta.get('filepath') not in used_image_filepaths and \
               i < image_bge_embeddings.shape[0] and \
               np.any(image_bge_embeddings[i]): # Ensure embedding exists and is not all zero
                candidate_meta_indices.append(i)
    
    if not candidate_meta_indices:
        logger.info("Round 3: No unused candidate images with valid embeddings available. Skipping.")
        return
    logger.info(f"Round 3: Found {len(candidate_meta_indices)} unused candidate images with embeddings for assignment.")

    candidate_filtered_metadata = [filtered_metadata[i] for i in candidate_meta_indices]
    candidate_bge_embeddings = image_bge_embeddings[candidate_meta_indices] if image_bge_embeddings is not None else None


    # 4. Compute paragraph embeddings for *all* paragraphs once, then select.
    # This matches the user's original simpler approach for this function.
    all_para_texts = [p.get('paragraph_text','') for p in paragraphs]
    all_para_embeddings = None
    if any(t.strip() for t in all_para_texts):
        all_para_embeddings = encode_text_bge(all_para_texts, device)
    else:
        logger.warning("Round 3: All paragraph texts in the script are empty. Using zero embeddings.")
        all_para_embeddings = np.zeros((len(all_para_texts), BGE_EMBEDDING_DIM), dtype=np.float32)


    # 5. Build similarity scores for each unassigned paragraph
    # candidate_scores: {orig_para_idx: [(local_candidate_idx, score, None), ...]}
    # where local_candidate_idx is index into candidate_filtered_metadata
    candidate_scores_for_assignment = {}
    relevant_para_indices_for_scoring = unassigned_single + unassigned_multi

    for para_idx in relevant_para_indices_for_scoring:
        if all_para_embeddings is None:
            continue  # Should not happen if check above is correct
        para_embedding = all_para_embeddings[para_idx]
        if not np.any(para_embedding) or candidate_bge_embeddings is None or candidate_bge_embeddings.shape[0] == 0:
            continue
        similarities = np.dot(para_embedding, candidate_bge_embeddings.T)  # (1xD) . (Ncand x D).T = (1 x Ncand)

        # build and sort all BGE scores, take top M, then filter by threshold
        all_scores = [(i, float(similarities[i])) for i in range(candidate_bge_embeddings.shape[0])]
        all_scores.sort(key=lambda x: x[1], reverse=True)
        top_scores = all_scores[:top_m_r3]
        current_para_sims = [(idx, score, None) for idx, score in top_scores if score >= r3_thr]

        if current_para_sims:
            candidate_scores_for_assignment[para_idx] = current_para_sims

    # The filtering of negative scores is now handled by the threshold above.
    # (Old negative-score filtering block removed.)

    # 6. Assign single-image paragraphs via ILP
    ilp_eligible_paras = [p_idx for p_idx in unassigned_single if p_idx in candidate_scores_for_assignment]
    
    if ilp_eligible_paras and candidate_filtered_metadata:
        logger.info(f"Round 3a ILP: Processing {len(ilp_eligible_paras)} single-image paragraphs.")
        scores_for_ilp = {p: candidate_scores_for_assignment[p] for p in ilp_eligible_paras}
        
        ilp_assignments = enhanced_ilp_optimization(
            ilp_eligible_paras,
            scores_for_ilp,
            candidate_filtered_metadata, 
            candidate_bge_embeddings,
            max_usage_per_image=max_usage_per_image,
            min_distance_between_reuse=min_distance_between_reuse,
            prioritize_unused=True,
            null_image_reward=0.0 
        )
        assigned_ilp_count = 0
        for para_idx, assigned_img_path in ilp_assignments:
            if assigned_img_path is not None:
                assigned_score = 0.0
                if para_idx in scores_for_ilp:
                    for local_cand_idx, score, _ in scores_for_ilp[para_idx]:
                        if 0 <= local_cand_idx < len(candidate_filtered_metadata) and \
                           candidate_filtered_metadata[local_cand_idx].get('filepath') == assigned_img_path:
                            assigned_score = score
                            break
                
                paragraphs[para_idx]['assigned_media'] = {
                    'type': 'image',
                    'filepath': assigned_img_path,
                    'similarity': assigned_score, 
                    'clip_similarity': None, 
                    'source_round': 3
                }
                used_image_filepaths.add(assigned_img_path) # Add to used set
                assigned_ilp_count +=1
        logger.info(f"Round 3a ILP: Assigned images to {assigned_ilp_count} single-image paragraphs.")
    else:
        logger.info("Round 3a ILP: Skipped.")

    # 7. Assign multi-image paragraphs greedily
    greedy_eligible_paras = [p_idx for p_idx in unassigned_multi if p_idx in candidate_scores_for_assignment]
    assigned_greedy_count = 0
    if greedy_eligible_paras and candidate_filtered_metadata:
        logger.info(f"Round 3b Greedy: Processing {len(greedy_eligible_paras)} multi-image paragraphs.")
        for para_idx in greedy_eligible_paras:
            if paragraphs[para_idx].get('assigned_media', {}).get('type') == 'image' and \
               paragraphs[para_idx]['assigned_media'].get('source_round') == 3:
                continue # Already assigned by ILP in this round

            num_total_needed = paragraphs[para_idx].get('num_images_needed', 1)
            
            existing_filepaths_for_para = []
            current_media_info = paragraphs[para_idx].get('assigned_media', {})
            if current_media_info.get('type') in ['multi_image', 'multi_image_pending']:
                existing_filepaths_for_para = list(current_media_info.get('filepaths', []))

            num_still_needed = num_total_needed - len(existing_filepaths_for_para)
            if num_still_needed <= 0: continue

            para_cand_scores_sorted = candidate_scores_for_assignment.get(para_idx, [])
            newly_assigned_for_this_para = []
            current_best_score = 0.0

            for local_cand_idx, score, _ in para_cand_scores_sorted:
                if len(newly_assigned_for_this_para) >= num_still_needed: break
                
                image_path = candidate_filtered_metadata[local_cand_idx].get('filepath')
                
                if image_path and image_path not in used_image_filepaths and \
                   image_path not in existing_filepaths_for_para and \
                   image_path not in newly_assigned_for_this_para:
                    newly_assigned_for_this_para.append(image_path)
                    used_image_filepaths.add(image_path) 
                    if score > current_best_score: current_best_score = score
            
            if newly_assigned_for_this_para:
                final_paths = existing_filepaths_for_para + newly_assigned_for_this_para
                assigned_media_update = {
                    'type': 'multi_image', # Default to multi_image
                    'filepaths': final_paths,
                    'similarity': current_best_score,
                    'clip_similarity': None,
                    'source_round': 3
                }
                if len(final_paths) < num_total_needed:
                    assigned_media_update['needs_generated_image'] = True
                    assigned_media_update['img_need_generate'] = num_total_needed - len(final_paths)
                    assigned_media_update['type'] = 'multi_image_pending' 
                else:
                    assigned_media_update.pop('needs_generated_image', None)
                    assigned_media_update.pop('img_need_generate', None)

                paragraphs[para_idx]['assigned_media'] = assigned_media_update
                assigned_greedy_count +=1
        logger.info(f"Round 3b Greedy: Added/completed images for {assigned_greedy_count} multi-image paragraphs.")
    else:
        logger.info("Round 3b Greedy: Skipped.")
    logger.info("Finished Round 3: Assign remaining existing images.")

# --- Main Assignment Logic (Updated to match image_assignment.py flow) ---

def assign_images_enhanced(
    paragraphs: List[Dict],
    image_dir: str,
    clip_dir: Optional[str], # Made optional
    theme: str,
    using_anchor: bool = False,
    generate_image: bool = False,
    max_anchor_sec: float = 30.0 # Keep anchor logic
) -> List[Dict]:
    """
    Enhanced assignment using BGE/BM25/Reranker/CLIP, but following the
    workflow defined in image_assignment.py:
    1. High-similarity image assignment (prioritizing single, then multi).
    2. Clip assignment.
    3. Assignment of remaining existing images.
    4. Optional placeholder generation OR fallback assignment (anchor, final ILP).
    """
    try:
        # 1. Initial Metadata Update & Enrichment
        attribute_json_path = get_image_metadata_path(image_dir, theme)
        # Basic update (e.g., find new files, remove deleted)
        update_attribute_json(attribute_json_path, image_dir)
        # Adds captions, tags, and summary using BLIP, Rekognition, LLM
        enrich_image_metadata(attribute_json_path, image_dir, theme, device, DRY_RUN)

        # 2. Get Valid Images and Load Metadata
        valid_image_paths = get_valid_images(image_dir)
        valid_image_paths = list(dict.fromkeys([os.path.abspath(img) for img in valid_image_paths]))
        logger.info(f"Valid images found after enrichment: {len(valid_image_paths)}")

        if not valid_image_paths:
            logger.warning("No valid images found. Skipping assignment.")
            if generate_image:
                # Mark all non-anchor paragraphs as pending
                for i, para in enumerate(paragraphs):
                    if para.get('assigned_media', {}).get('type') != 'anchor':
                        paragraphs[i]['assigned_media'] = {
                            'type': 'pending',
                            'needs_generated_image': True,
                            'img_need_generate': para.get('num_images_needed', 1)
                        }
                logger.info("Marked all assignable paragraphs for image generation as no valid images were found.")
            return paragraphs

        try:
            with open(attribute_json_path, 'r', encoding='utf-8') as f:
                image_metadata = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Failed to load metadata file {attribute_json_path}: {e}. Cannot proceed.")
            return paragraphs

        # 3. Build Indices, Get Embeddings and Candidates
        faiss_index, bm25_index, faiss_id_to_meta_idx, filtered_metadata, image_bge_embeddings = build_bge_index_and_data(
            image_metadata,
            valid_image_paths,
            theme,
            attribute_json_path,
            device
        )

        if faiss_index is None or not filtered_metadata:
            logger.error("Failed to build necessary indices or find valid data. Aborting assignment.")
            if generate_image:
                 # Mark as pending if generate_image is true
                 for i, para in enumerate(paragraphs):
                     if para.get('assigned_media', {}).get('type') != 'anchor':
                         paragraphs[i]['assigned_media'] = {
                             'type': 'pending',
                             'needs_generated_image': True,
                             'img_need_generate': para.get('num_images_needed', 1)
                         }
                 logger.info("Marked all assignable paragraphs for image generation as index building failed.")
            return paragraphs

        # Calculate Similarity, Rerank, Fuse Scores
        # Returns: Dict[int, List[Tuple[int, float]]] -> {para_idx: [(meta_idx, fused_score), ...]}
        # Note: This uses the reranker score as the primary score now.
        reranked_results = calculate_bge_similarity_and_rerank(
            paragraphs,
            device,
            faiss_index,
            bm25_index,
            faiss_id_to_meta_idx,
            filtered_metadata
        )

        # Apply CLIP Visual Post-Filter
        # Returns: Dict[int, List[Tuple[int, float, Optional[float]]]] -> {para_idx: [(meta_idx, rerank_score, clip_score), ...]}
        final_candidates = apply_clip_post_filter(
            reranked_results,
            paragraphs,
            filtered_metadata,
            device
        )

        # ---------- Round-1 阈值 ----------
        # Access args from the main function context where parse_args() result is available
        # We need to pass 'args' to assign_images_enhanced or access it globally if set in main
        # Assuming args is accessible here. If not, it needs to be passed as an argument.
        # For now, let's assume it's accessible via a closure or global scope (less ideal)
        # A better approach would be to pass 'args' to this function.
        # Let's modify the function signature later if needed.
        # We'll access it via the global `args` variable set in `main` for now.
        global args # Need to declare global if accessing args set in main()
        user_thr = getattr(args, "reranker_threshold", None)  # argparse 会自动给到
        if user_thr is not None:
            RERANKER_SCORE_THRESHOLD_FIRST = user_thr
            logger.info(f"使用用户指定的 reranker 阈值: {RERANKER_SCORE_THRESHOLD_FIRST:.4f}")
        else:
            RERANKER_SCORE_THRESHOLD_FIRST = RERANKER_SCORE_MIN_THRESHOLD
            logger.info(f"使用默认 reranker 阈值: {RERANKER_SCORE_THRESHOLD_FIRST:.4f}")
        # ----------------------------------

        # --- Round 1: High Similarity Image Assignment ---
        logger.info(f"Round 1: High Similarity Image Assignment (Reranker Score > {RERANKER_SCORE_THRESHOLD_FIRST:.4f})")

        # Identify paragraphs needing assignment (not already assigned anchor/clip)
        unassigned_single_indices_r1 = []
        unassigned_multi_indices_r1 = []
        for i, para in enumerate(paragraphs):
            media_type = para.get('assigned_media', {}).get('type')
            # Assign if 'pending' or not set, exclude 'anchor' and 'clip'
            if media_type == 'pending' or media_type is None:
                num_needed = para.get('num_images_needed', 1)
                if num_needed == 1:
                    unassigned_single_indices_r1.append(i)
                elif num_needed > 1:
                     # Check if already partially assigned (shouldn't happen here, but safety check)
                     filepaths = para.get('assigned_media', {}).get('filepaths', [])
                     if len(filepaths) < num_needed:
                         unassigned_multi_indices_r1.append(i)
                # else: # num_needed <= 0? ignore
            # Handle case where a multi_image was somehow assigned but incomplete
            elif media_type == 'multi_image':
                num_needed = para.get('num_images_needed', 1)
                filepaths = para.get('assigned_media', {}).get('filepaths', [])
                if len(filepaths) < num_needed:
                    unassigned_multi_indices_r1.append(i)


        logger.info(f"Round 1: Found {len(unassigned_single_indices_r1)} single-image and {len(unassigned_multi_indices_r1)} multi-image paragraphs needing assignment.")

        # Prepare high-similarity candidates for ILP and greedy assignment
        high_sim_candidates = {}
        high_sim_meta_indices_set = set()
        logger.debug("--- R1 Candidate Scores (Before Threshold) ---")
        for para_idx, candidates in final_candidates.items():
            # Skip paragraphs already assigned (e.g., anchor)
            if paragraphs[para_idx].get('assigned_media', {}).get('type') not in ['pending', None, 'multi_image_pending']:
                 continue

            para_high_sim = []
            # Sort candidates by rerank_score to easily see the best ones
            sorted_candidates = sorted(candidates, key=lambda x: x[1], reverse=True)
            # Take only the top‐K highest‐scoring candidates, then sort
            
            for i, (meta_idx, rerank_score, clip_score) in enumerate(sorted_candidates):
                 # Log top few scores for brevity
                #  if i < 5: # Log only top 5 scores
                #     logger.debug(f"  Rank {i+1}: MetaIdx {meta_idx}, Rerank={rerank_score:.4f}, CLIP={clip_score}")

                 # Existing threshold check
                 if rerank_score >= RERANKER_SCORE_THRESHOLD_FIRST:
                     para_high_sim.append((meta_idx, rerank_score, clip_score))
                     high_sim_meta_indices_set.add(meta_idx)
                 # else: # Optional: log scores that *failed* the check (might be too verbose)
                 #    if i < 5: logger.debug(f"    -> Failed Threshold ({RERANKER_SCORE_THRESHOLD_FIRST:.4f})")

            if para_high_sim:
                high_sim_candidates[para_idx] = para_high_sim # Store the filtered high-sim candidates
        logger.debug("--- End R1 Candidate Scores ---")

        if not high_sim_meta_indices_set:
             logger.warning("Round 1: No images met the high similarity threshold. Skipping image assignment for this round.")
        else:
            # Filter metadata and embeddings for high-similarity images
            high_sim_meta_indices_list = sorted(list(high_sim_meta_indices_set))
            high_sim_filtered_metadata = [filtered_metadata[i] for i in high_sim_meta_indices_list if 0 <= i < len(filtered_metadata)]
            # Map original meta_idx to its new index in the high_sim list
            meta_idx_to_high_sim_idx = {orig_idx: new_idx for new_idx, orig_idx in enumerate(high_sim_meta_indices_list)}

            # Adjust high_sim_candidates to use the new high_sim index
            high_sim_candidates_adjusted = {}
            for para_idx, candidates in high_sim_candidates.items():
                 adjusted_cand = []
                 for meta_idx, rerank_score, clip_score in candidates:
                      if meta_idx in meta_idx_to_high_sim_idx:
                           new_meta_idx = meta_idx_to_high_sim_idx[meta_idx]
                           adjusted_cand.append((new_meta_idx, rerank_score, clip_score))
                 if adjusted_cand:
                      high_sim_candidates_adjusted[para_idx] = adjusted_cand

            # Get corresponding embeddings
            high_sim_bge_embeddings = None
            if image_bge_embeddings is not None:
                try:
                     high_sim_bge_embeddings = image_bge_embeddings[high_sim_meta_indices_list]
                except IndexError:
                     logger.error("Error slicing BGE embeddings for high similarity round. Skipping ILP diversity.")
                     high_sim_bge_embeddings = None


            # Track image usage within Round 1
            used_image_paths_round1 = set()
            assigned_paras_r1 = set()

            # 1a. Prioritize Single-Image Paragraphs with ILP
            if unassigned_single_indices_r1 and high_sim_filtered_metadata:
                logger.info(f"Round 1a: Assigning high-similarity images to {len(unassigned_single_indices_r1)} single-image paragraphs via ILP (max_usage=1).")
                # Ensure we only pass relevant paragraphs to ILP
                paras_for_ilp_r1 = [p for p in unassigned_single_indices_r1 if p in high_sim_candidates_adjusted]
                if paras_for_ilp_r1:
                    # Pass adjusted candidates and high_sim metadata/embeddings
                    round1_single_assignments = enhanced_ilp_optimization(
                        paras_for_ilp_r1,
                        high_sim_candidates_adjusted, # Use adjusted candidates with new meta indices
                        high_sim_filtered_metadata,   # Use filtered metadata
                        high_sim_bge_embeddings,      # Use filtered embeddings
                        max_usage_per_image=1,        # Strict single use
                        min_distance_between_reuse=3, # Less relevant here, but keep default
                        prioritize_unused=True,       # Prioritize coverage
                        null_image_reward=0.0 # Penalize null assignments strongly in this round
                    )

                    # Update paragraph assignments and track usage
                    for para_idx, img_path in round1_single_assignments:
                        if img_path is not None:
                            assigned_rerank_score = 0.0
                            assigned_clip_score = None
                            # Find scores from original high_sim_candidates (using original meta_idx)
                            original_meta_idx = -1
                            if para_idx in high_sim_candidates:
                                for meta_idx, rerank_score, clip_score in high_sim_candidates[para_idx]:
                                     # Check if metadata entry exists and path matches
                                     if 0 <= meta_idx < len(filtered_metadata) and filtered_metadata[meta_idx].get('filepath') == img_path:
                                         assigned_rerank_score = rerank_score
                                         assigned_clip_score = clip_score
                                         original_meta_idx = meta_idx # For logging if needed
                                         break

                            paragraphs[para_idx]['assigned_media'] = {
                                'type': 'image',
                                'filepath': img_path,
                                'duration': None, 'start_time': None, 'end_time': None,
                                'similarity': float(assigned_rerank_score),
                                'clip_similarity': assigned_clip_score
                            }
                            used_image_paths_round1.add(img_path)
                            assigned_paras_r1.add(para_idx)
                            # logger.debug(f"R1a ILP: Para {para_idx} assigned {os.path.basename(img_path)} (Score: {assigned_rerank_score:.4f}, MetaIdx: {original_meta_idx})")
                        # else: Para assigned null by ILP, remains unassigned for now

                    logger.info(f"Round 1a: ILP assigned images to {len(assigned_paras_r1)} single-image paragraphs.")
                else:
                    logger.info("Round 1a: No single-image paragraphs had high-similarity candidates.")
            else:
                logger.info("Round 1a: No single-image paragraphs to assign or no high-similarity images found.")

            # 1b. Handle Multi-Image Paragraphs Greedily
            if unassigned_multi_indices_r1 and high_sim_filtered_metadata:
                logger.info(f"Round 1b: Assigning high-similarity images to {len(unassigned_multi_indices_r1)} multi-image paragraphs.")
                assigned_multi_count = 0
                for para_idx in unassigned_multi_indices_r1:
                     # Skip if already assigned by ILP somehow (shouldn't happen)
                     if para_idx in assigned_paras_r1: continue

                     # Get needed count, considering any potentially existing images
                     num_total_needed = paragraphs[para_idx].get('num_images_needed', 1)
                     existing_filepaths = paragraphs[para_idx].get('assigned_media', {}).get('filepaths', [])
                     num_still_needed = num_total_needed - len(existing_filepaths)

                     if num_still_needed <= 0: continue # Already fulfilled

                     # Get high-similarity candidates for this paragraph (using original meta_idx)
                     candidates_for_para = sorted(
                          high_sim_candidates.get(para_idx, []),
                          key=lambda x: x[1], # Sort by rerank_score (index 1)
                          reverse=True
                     )

                     newly_assigned_filepaths = []
                     for meta_idx, rerank_score, clip_score in candidates_for_para:
                          if len(newly_assigned_filepaths) >= num_still_needed: break # Found enough

                          # Check if metadata entry is valid
                          if not (0 <= meta_idx < len(filtered_metadata)): continue

                          current_path = filtered_metadata[meta_idx].get('filepath')
                          # Check if path is valid, not already used in this round, and not already in existing paths
                          if current_path and current_path not in used_image_paths_round1 and current_path not in existing_filepaths:
                               newly_assigned_filepaths.append(current_path)
                               used_image_paths_round1.add(current_path) # Mark as used for R1

                     # MODIFIED: Assign if *any* new high-sim images are found
                     if newly_assigned_filepaths:
                          final_filepaths = existing_filepaths + newly_assigned_filepaths
                          # Use score of the best newly added image
                          best_new_score = 0.0
                          best_clip_score = None
                          # Find the best score among the *newly added* candidates
                          newly_added_scores = []
                          for meta_idx, rerank_score, clip_score in candidates_for_para:
                              cand_path = filtered_metadata[meta_idx].get('filepath') if 0 <= meta_idx < len(filtered_metadata) else None
                              if cand_path in newly_assigned_filepaths:
                                  newly_added_scores.append((rerank_score, clip_score))
                          if newly_added_scores:
                              newly_added_scores.sort(key=lambda x: x[0], reverse=True)
                              best_new_score = newly_added_scores[0][0]
                              best_clip_score = newly_added_scores[0][1]

                          paragraphs[para_idx]['assigned_media'] = {
                              'type': 'multi_image', # Assign as multi_image even if partially filled
                              'filepaths': final_filepaths,
                              'duration': None, 'start_time': None, 'end_time': None,
                              'similarity': float(best_new_score),
                              'clip_similarity': best_clip_score
                          }
                          assigned_paras_r1.add(para_idx)
                          assigned_multi_count += 1
                          #logger.debug(f"R1b Greedy: Para {para_idx} assigned {len(newly_assigned_filepaths)} new images (total {len(final_filepaths)} of {num_total_needed} needed).")
                     # else: # No new high-sim images found for this para
                     #    logger.debug(f"R1b Greedy: Para {para_idx} could not find any unused high-sim images. Remains unassigned for R1.")

                logger.info(f"Round 1b: Assigned images to {assigned_multi_count} multi-image paragraphs (potentially partial assignments).")
            else:
                logger.info("Round 1b: No multi-image paragraphs to assign or no high-similarity images found.")

        logger.info("Round 1: High Similarity Assignment finished.")
        print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Round 1 - High Similarity")


        # --- Round 2: Video Clip Assignment ---
        # Re-identify unassigned paragraphs after Round 1
        unassigned_indices_r2 = [
            i for i, para in enumerate(paragraphs)
            if para.get('assigned_media', {}).get('type') in ['pending', None] or \
               (para.get('assigned_media', {}).get('type') == 'multi_image' and \
                len(para.get('assigned_media', {}).get('filepaths', [])) < para.get('num_images_needed', 1))
        ]

        logger.info(f"Round 2: Video Clip Assignment for {len(unassigned_indices_r2)} paragraphs.")
        if clip_dir and os.path.exists(clip_dir) and unassigned_indices_r2:
            clips_data = load_clips_data(clip_dir, theme)
            if clips_data:
                # Calculate video clip similarity matrix using BGE
                clip_descriptions = [clip.get('description', '') for clip in clips_data]
                # Encode paragraphs and descriptions using BGE
                # Encode all paras once, reuse embeddings
                # Ensure paragraph_embeddings_r2 is calculated if not already available
                if 'paragraph_embeddings_r2' not in locals(): # Calculate if not done already
                     paragraph_texts_all = [p.get('paragraph_text', '') for p in paragraphs]
                     paragraph_embeddings_r2 = encode_text_bge(paragraph_texts_all, device)

                clip_embeddings = encode_text_bge(clip_descriptions, device)

                # Build similarity matrix only for relevant paras and valid clips
                clip_similarity_matrix = np.zeros((len(paragraphs), len(clip_descriptions)))
                valid_clip_embed_mask = np.any(clip_embeddings, axis=1)
                valid_clip_indices = np.where(valid_clip_embed_mask)[0]
                valid_clip_embeddings = clip_embeddings[valid_clip_indices]

                if valid_clip_embeddings.shape[0] > 0:
                    for i in unassigned_indices_r2: # Only calculate for needed paras
                        para_embedding = paragraph_embeddings_r2[i]
                        if not np.any(para_embedding): continue # Skip if para embed failed
                        sims = np.dot(para_embedding, valid_clip_embeddings.T)
                        clip_similarity_matrix[i, valid_clip_indices] = sims

                # Assign clips (using the original assign_clips logic from image_assignment)
                assign_clips(
                    paragraphs, # Modifies paragraphs in-place
                    clips_data,
                    clip_similarity_matrix,
                    unassigned_indices=unassigned_indices_r2, # Tell it which ones to focus on
                    using_anchor=using_anchor # Pass anchor status if needed by assign_clips
                )
                logger.info("Round 2: Clip assignment finished.")
            else:
                logger.info("Round 2: No clip data found.")
        else:
            logger.info("Round 2: Skipping clip assignment (no dir, no path, or no unassigned paras).")

        logger.info("Assignment Status After Round 2 (Clip Assignment):")
        print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Round 2 - Clip Assignment")

        # --- Round 3: Assign remaining existing images (New) ---
        logger.info("Initiating Round 3: Assign remaining existing images.")
        if filtered_metadata and image_bge_embeddings is not None:
             assign_remaining_images(
                 paragraphs,
                 filtered_metadata, 
                 image_bge_embeddings, 
                 device,
                 max_usage_per_image=1, # Default, can be tuned via args if exposed
                 min_distance_between_reuse=2 # Default, can be tuned
             )
        else:
            logger.info("Round 3: Skipped assignment of remaining images (no metadata/embeddings).")
        logger.info("Assignment Status After Round 3 (Remaining Images Assignment):")
        print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Round 3 - Remaining Images")


        # --- Round 4: Placeholder Generation or Fallback --- (Renumbered from Round 3)
        # Identify final unassigned paragraphs (including partially assigned multi-image)
        unassigned_indices_final = [
            i for i, para in enumerate(paragraphs)
            if para.get('assigned_media', {}).get('type') in ['pending', None] or \
               (para.get('assigned_media', {}).get('type') == 'multi_image' and \
                len(para.get('assigned_media', {}).get('filepaths', [])) < para.get('num_images_needed', 1))
        ]
        logger.info(f"End of Round 3 (Image Assignments): {len(unassigned_indices_final)} paragraphs remain unassigned or partially assigned before fallback/generation.")

        # Option 1: Generate Placeholders
        if generate_image and unassigned_indices_final:
            logger.info("Round 4: Creating image generation placeholders...")
            placeholder_count = 0
            for idx in unassigned_indices_final:
                media_data = paragraphs[idx].get('assigned_media', {})
                num_total_needed = paragraphs[idx].get('num_images_needed', 1)
                existing_filepaths = media_data.get('filepaths', []) if media_data.get('type') == 'multi_image' else []
                num_still_needed = num_total_needed - len(existing_filepaths)

                if num_still_needed > 0:
                    if num_total_needed == 1: # Single image needed
                        paragraphs[idx]['assigned_media'] = {
                            'type': 'pending',
                            'needs_generated_image': True,
                            'img_need_generate': 1
                        }
                        logger.debug(f"Paragraph {idx} marked for single image generation.")
                        placeholder_count += 1
                    else: # Multi-image needed (potentially partially filled)
                        paragraphs[idx]['assigned_media'] = {
                            'type': 'multi_image_pending',
                            'needs_generated_image': True,
                            'img_need_generate': num_still_needed,
                            'filepaths': existing_filepaths # Preserve existing ones
                        }
                        logger.debug(f"Paragraph {idx} marked for multi-image generation ({num_still_needed} needed, {len(existing_filepaths)} existing).")
                        placeholder_count += num_still_needed

            if placeholder_count > 0:
                logger.info(f"Round 4: Created placeholders for {placeholder_count} images.")
            logger.info("Final Assignment Status (with placeholders):")
            print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Round 4 - Placeholders Generated")
            return paragraphs

        # Option 2: Fallback Assignment (if not generate_image and paras remain unassigned)
        elif unassigned_indices_final:
            logger.info(f"Round 4: Fallback Assignment for {len(unassigned_indices_final)} paragraphs.")

            # 4a. Anchor Assignment (copied from image_assignment.py logic)
            if using_anchor:
                logger.info("Round 4a: Applying anchor fallback.")
                total_paragraphs = len(paragraphs)
                max_anchor_fraction = 0.1 # Max 10% anchors total
                # Count existing anchors (could be initial or from clip logic if it assigns anchors)
                current_anchor_count = sum(1 for p in paragraphs if p.get('assigned_media',{}).get('type') == 'anchor')
                max_anchor_count = math.ceil(total_paragraphs * max_anchor_fraction)
                allowed_new_anchors = max_anchor_count - current_anchor_count

                if allowed_new_anchors > 0:
                    # Select candidates from the currently unassigned list
                    anchor_candidates = unassigned_indices_final[:min(allowed_new_anchors, len(unassigned_indices_final))]
                    for idx in anchor_candidates:
                        paragraphs[idx]['assigned_media'] = {
                            'type': 'anchor',
                            'filepath': None,
                            'duration': paragraphs[idx].get('audio', {}).get('duration', 0),
                            'start_time': None,
                            'end_time': None
                        }
                    logger.info(f"Round 4a: Assigned {len(anchor_candidates)} new anchors.")
                    # Update the list of paragraphs still needing assignment
                    unassigned_indices_final = [idx for idx in unassigned_indices_final if idx not in anchor_candidates]
                    logger.info(f"Round 4a: {len(unassigned_indices_final)} paragraphs remaining after anchor fallback.")
                else:
                    logger.info("Round 4a: No more anchor quota available or no unassigned paragraphs.")
            else:
                 logger.info("Round 4a: Anchor fallback skipped (using_anchor=False).")

            # 4b. Final ILP Assignment
            if unassigned_indices_final and filtered_metadata:
                logger.info(f"Round 4b: Applying ILP fallback for {len(unassigned_indices_final)} paragraphs.")
                # Use all available filtered images as candidates
                num_fallback_images = len(filtered_metadata)
                # Calculate dynamic max usage, similar to image_assignment.py
                max_usage_fallback = 3 # Default
                if num_fallback_images > 0:
                    # Allow potentially high reuse if few images and many paras
                    max_usage_fallback = min(max(3, MAX_TOTAL_USES), math.ceil(len(unassigned_indices_final) / num_fallback_images) + 2)
                logger.info(f"Fallback ILP allowing max usage per image: {max_usage_fallback}")

                # Determine fallback null reward (use a small fixed value or percentile)
                all_scores_fallback = []
                for para_idx in unassigned_indices_final:
                    candidates = final_candidates.get(para_idx, [])
                    if candidates:
                        all_scores_fallback.extend([rerank_score for _, rerank_score, _ in candidates])
                fallback_null_reward = 0.01 # Default small reward
                if all_scores_fallback:
                    try:
                        fallback_null_reward = max(0.001, np.percentile(all_scores_fallback, 10) - 0.02) # Slightly below 10th percentile
                    except IndexError: pass
                logger.info(f"Fallback ILP null image reward: {fallback_null_reward:.4f}")

                # Run ILP with all candidates, relaxed constraints
                fallback_assignments = enhanced_ilp_optimization(
                    unassigned_indices_final,
                    final_candidates, # Pass all candidates
                    filtered_metadata, # Use all metadata
                    image_bge_embeddings, # Use all embeddings
                    max_usage_per_image=max_usage_fallback,
                    min_distance_between_reuse=2, # Relax reuse distance slightly
                    prioritize_unused=False, # Focus on best match in fallback
                    null_image_reward=fallback_null_reward
                )

                # Update assignments, handling multi-image duplication
                assigned_fallback_count = 0
                for para_idx, img_path in fallback_assignments:
                    if img_path is not None:
                        assigned_fallback_count += 1
                        num_needed = paragraphs[para_idx].get('num_images_needed', 1)
                        assigned_rerank_score = 0.0
                        assigned_clip_score = None
                        # Find scores from final_candidates
                        if para_idx in final_candidates:
                            for meta_idx, rerank_score, clip_score in final_candidates[para_idx]:
                                if 0 <= meta_idx < len(filtered_metadata) and filtered_metadata[meta_idx].get('filepath') == img_path:
                                    assigned_rerank_score = rerank_score
                                    assigned_clip_score = clip_score
                                    break
                        
                        # Apply duplication logic for multi-image paragraphs (like image_assignment.py)
                        if num_needed == 1:
                            paragraphs[para_idx]['assigned_media'] = {
                                'type': 'image',
                                'filepath': img_path,
                                'duration': None, 'start_time': None, 'end_time': None,
                                'similarity': float(assigned_rerank_score),
                                'clip_similarity': assigned_clip_score,
                                'fallback_assignment': True
                            }
                        elif num_needed > 1:
                             paragraphs[para_idx]['assigned_media'] = {
                                 'type': 'multi_image',
                                 'filepaths': [img_path] * num_needed, # Duplicate the assigned image
                                 'duration': None, 'start_time': None, 'end_time': None,
                                 'similarity': float(assigned_rerank_score), # Score of the duplicated image
                                 'clip_similarity': assigned_clip_score,
                                 'fallback_assignment': True
                             }
                    else:
                        # Paragraph assigned null by ILP
                        paragraphs[para_idx]['assigned_media'] = {
                            'type': 'null', # Mark explicitly as null
                            'filepath': None,
                            'duration': paragraphs[para_idx].get('audio', {}).get('duration', 0),
                            'start_time': None, 'end_time': None,
                            'similarity': 0.0,
                            'fallback_assignment': True
                        }
                logger.info(f"Round 4b: ILP fallback assigned images/null to {len(fallback_assignments)} paragraphs ({assigned_fallback_count} non-null).")

            elif not filtered_metadata:
                logger.warning("Round 4b: Skipping ILP fallback as no image metadata is available.")
            elif not unassigned_indices_final:
                 logger.info("Round 4b: Skipping ILP fallback as no paragraphs remain unassigned.")

        else:
            # Case where not generate_image, but no paras were unassigned after clips/anchors
            logger.info("No remaining unassigned paragraphs after Round 2/Anchor assignment.")


        # --- Final Status ---
        logger.info("Final Assignment Status:")
        print_assignment_status(paragraphs, len(filtered_metadata) if filtered_metadata else 0, "Final Fallback or No Further Action")

        return paragraphs

    except Exception as e:
        logger.error(f"Error in enhanced image assignment: {str(e)}")
        logger.debug("Error details:", exc_info=True)
        # Return partially processed paragraphs on error
        return paragraphs

# --- Script Processing and Main --- (Largely unchanged, uses updated assign_images_enhanced)

def process_script(
    script_path: str,
    using_anchor: bool = False,
    max_anchor_sec: float = 30.0
) -> List[Dict[str, Any]]:
    """Process script file, segment text and prepare for image matching"""
    try:
        # Read JSON file
        with open(script_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # Build paragraph structure
        paragraphs = []
        for item in data:
            paragraph = {
                'segment_id': item['segment_id'],
                'paragraph_text': item['paragraph_text'],
                'audio': item['audio']  # Keep original audio info
            }
            
            # Calculate needed images based on duration
            if 'audio' in paragraph and 'duration' in paragraph['audio']:
                duration = paragraph['audio']['duration']
                # Fix E-8: Prevent division by zero or negative duration
                if duration and duration > 0:
                    num_images = calculate_num_images_needed(duration)
                else:
                    num_images = 1
                    logger.warning(f"Paragraph {item['segment_id']} has invalid duration {duration}, defaulting to 1 image")
                paragraph['num_images_needed'] = num_images
                # logger.info(f"Paragraph {item['segment_id']} duration {duration:.2f}s, needs {num_images} images") # Logged later if needed
            else:
                paragraph['num_images_needed'] = 1
                logger.warning(f"Paragraph {item['segment_id']} missing audio duration, defaulting to 1 image")
            
            paragraphs.append(paragraph)
        
        # Initialize assigned_media field
        if using_anchor and paragraphs:
            total_duration = 0
            max_anchor_duration = max_anchor_sec
            
            for i, paragraph in enumerate(paragraphs):
                # Initialize all as pending
                paragraph['assigned_media'] = {
                    'type': 'pending',
                    'filepath': None,
                    'duration': paragraph['audio']['duration'],
                    'start_time': None,
                    'end_time': None
                }
                
                # First paragraph always anchor
                if i == 0:
                    paragraph['assigned_media']['type'] = 'anchor'
                    total_duration = paragraph['audio']['duration']
                    logger.info(f"Paragraph {paragraph['segment_id']} set as anchor, cumulative duration: {total_duration:.2f}s")
                # Check if can extend anchor
                elif total_duration < max_anchor_duration:
                    current_duration = paragraph['audio']['duration']
                    if total_duration + current_duration <= max_anchor_duration:
                        paragraph['assigned_media']['type'] = 'anchor'
                        total_duration += current_duration
                        logger.info(f"Paragraph {paragraph['segment_id']} set as anchor, cumulative duration: {total_duration:.2f}s")
                    else:
                         pass # Remains pending
                         # logger.info(f"Paragraph {paragraph['segment_id']} remains pending (would exceed {max_anchor_duration}s)")
                # else: # logger.info(f"Paragraph {paragraph['segment_id']} remains pending (already at {max_anchor_duration}s)")
                    
            logger.info(f"Using anchor mode: {sum(1 for p in paragraphs if p['assigned_media']['type'] == 'anchor')} paragraphs as anchor")
        
        # Non-anchor mode initialization
        else:
            for paragraph in paragraphs:
                paragraph['assigned_media'] = {
                    'type': 'pending',
                    'filepath': None
                }
                # logger.info(f"Paragraph {paragraph['segment_id']} set as pending")
            
        logger.info(f"Successfully loaded {len(paragraphs)} paragraphs from {script_path}")
        return paragraphs
        
    except Exception as e:
        logger.error(f"Error processing script file {script_path}: {e}")
        raise

def save_results(paragraphs: List[Dict], output_path: str) -> None:
    """Save processed paragraph data to JSON file"""
    try:
        def convert_numpy_types(obj):
            """Convert numpy types to Python native types"""
            if isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, (np.integer, np.int32, np.int64)):
                return int(obj)
            elif isinstance(obj, (np.floating, np.float32, np.float64)):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            # 添加对 NumPy 布尔类型的处理
            elif isinstance(obj, (np.bool_, bool)):
                return bool(obj)
            # Add handling for potential faiss index objects if they leak? Should not happen.
            elif 'Index' in str(type(obj)): # Basic check
                 return f"<FAISS Index Object: {type(obj)}>"
            else:
                return obj
        
        # Convert numpy types and save
        sanitized_data = convert_numpy_types(paragraphs)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(sanitized_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"Results saved to: {output_path}")
        
    except Exception as e:
        logger.error(f"Error saving results to {output_path}: {str(e)}")
        logger.debug("Data structure causing save error (sample):", paragraphs[:2])
        raise

def get_theme_from_path(json_path: str) -> str:
    """Extract theme name from JSON file path"""
    filename = os.path.basename(json_path)
    theme = filename.replace('_audio.json', '').replace('_script.json', '') # More robust cleaning
    return theme

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Enhanced process: match paragraphs with images/clips using BGE, BM25, Reranker and CLIP post-filter")
    parser.add_argument("--json", type=str, required=True, help="Input JSON file path (e.g., *_script.json or *_audio.json)")
    parser.add_argument("--image_dir", type=str, required=True, help="Image directory path")
    parser.add_argument("--clip_dir", type=str, help="Video clip directory path (optional)")
    parser.add_argument("--theme", type=str, help="Theme name (optional, default: extracted from filename)")
    parser.add_argument("--using_anchor", action="store_true", help="Whether to use anchor mode")
    parser.add_argument("--generate_image", action="store_true", help="Create image placeholders for unassigned paragraphs instead of fallback assignment")
    parser.add_argument("--dry-run", action="store_true", help="Skip AWS API calls when working offline")
    parser.add_argument("--max-anchor-sec", type=float, default=30.0, help="Maximum cumulative duration (seconds) for initial anchor segments")
    # Add arguments for tuning if needed, e.g., --bm25-weight, --clip-threshold
    parser.add_argument(
        "--reranker-threshold", type=float, default=None,
        help=("Absolute minimum reranker similarity score required for高相似度筛选，"
              "若省略则使用脚本内默认 %.2f" % RERANKER_SCORE_MIN_THRESHOLD),
    )
    return parser.parse_args()

# Global variable to hold parsed arguments
args = None

def main():
    """Main function"""
    # Parse args and store globally
    global args
    args = parse_args()

    # Set global flag for dry run mode
    global DRY_RUN
    DRY_RUN = args.dry_run
    if DRY_RUN:
        logger.info("Running in dry-run mode: AWS API calls will be skipped")
    
    try:
        # Process script
        paragraphs = process_script(
            args.json, 
            using_anchor=args.using_anchor, 
            max_anchor_sec=args.max_anchor_sec
        )
        
        # Extract theme if not specified
        theme = args.theme
        if not theme:
            theme = get_theme_from_path(args.json)
            logger.info(f"Theme extracted from filename: {theme}")
        
        # Call the enhanced assignment function
        logger.info(f"Starting enhanced media assignment: image_dir={args.image_dir}, clip_dir={args.clip_dir}, generate_image={args.generate_image}")
        # No need to filter paragraphs beforehand, assign_images_enhanced handles logic internally
        final_paragraphs = assign_images_enhanced(
            paragraphs, # Pass the full list
            args.image_dir,
            args.clip_dir,
            theme,
            args.using_anchor,
            args.generate_image,
            args.max_anchor_sec # Pass anchor sec for consistency if needed inside
        )

        # Save results
        output_path = args.json.replace('_script.json', '_images.json').replace('_audio.json', '_images.json')
        save_results(final_paragraphs, output_path)
        
        # Print success message
        logger.info(f"Enhanced media assignment complete! Results saved to {output_path}")
        if args.generate_image:
            # Correctly sum images needed for both 'pending' and 'multi_image_pending' types
            img_need_generate_total = sum(p.get('assigned_media', {}).get('img_need_generate', 0)
                                  for p in final_paragraphs
                                  if p.get('assigned_media', {}).get('type') in ['pending', 'multi_image_pending'] # Check both types
                                  and p.get('assigned_media', {}).get('needs_generated_image', False))
            if img_need_generate_total > 0:
                logger.info(f"Created placeholders for {img_need_generate_total} images") # Use updated variable name
                logger.info(f"Next step: Use image_generation.py to generate images based on {output_path}")
        else:
            null_count = sum(1 for p in final_paragraphs if p.get('assigned_media', {}).get('type') == 'null')
            if null_count > 0:
                logger.info(f"{null_count} paragraphs were assigned 'null' as no suitable media was found.")

        
    except Exception as e:
        logger.error(f"Processing failed: {str(e)}")
        logger.debug("Error details:", exc_info=True)
        # Consider exiting with non-zero status on failure
        sys.exit(1)

if __name__ == "__main__":
    # Example command:
    # python assign_clip_image_new.py --json path/to/your_script.json --image_dir path/to/images --clip_dir path/to/clips --theme mytheme --using_anchor --generate_image
    main() 