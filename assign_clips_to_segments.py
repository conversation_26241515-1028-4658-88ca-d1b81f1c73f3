import argparse
import json
import os
import shutil
import sys
from collections import defaultdict
import logging

# 确保项目根目录（即此脚本所在的目录）在 sys.path 中
# 这使得我们可以使用相对于项目根目录的绝对导入路径
# project_root = os.path.dirname(os.path.abspath(__file__))
# if project_root not in sys.path:
#     sys.path.insert(0, project_root)

from video_retrieval import phase2_retrieval
from video_retrieval import video_config as config

# --- Constants ---
CLIP_COUNT = 1  # Default value, can be changed.

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# <<< START OF ADDED FUNCTIONS >>>
def load_input_data(json_path):
    """从指定的 JSON 文件中加载数据。"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        if not isinstance(data, list):
            logger.error("Input JSON must be a list of segments.")
            return None
        for segment in data:
            if not all(k in segment for k in ['segment_id', 'paragraph_text']):
                logger.error(f"Segment missing 'segment_id' or 'paragraph_text': {segment}")
                return None
        logger.info(f"Successfully loaded {len(data)} segments from {json_path}")
        return data
    except FileNotFoundError:
        logger.error(f"Input JSON file not found: {json_path}")
        return None
    except json.JSONDecodeError:
        logger.error(f"Error decoding JSON from file: {json_path}")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred while loading input data: {e}")
        return None

def retrieve_candidates_for_all_segments(segments_data, retrieval_resources):
    """为所有 segment 检索候选视频片段。"""
    all_candidates = []
    logger.info(f"Retrieving candidates for {len(segments_data)} segments.")

    # Add a retry mechanism for segments that fail
    max_retries = 2
    failed_segments = []

    for i, segment in enumerate(segments_data):
        segment_id = segment['segment_id']
        paragraph_text = segment['paragraph_text']
        logger.info(f"Processing segment {i+1}/{len(segments_data)}: ID '{segment_id}'")

        # Skip empty paragraphs
        if not paragraph_text or paragraph_text.strip() == "":
            logger.warning(f"Segment ID '{segment_id}' has empty paragraph text. Skipping.")
            continue

        # Limit paragraph length to avoid memory issues
        max_text_length = 1000
        if len(paragraph_text) > max_text_length:
            logger.warning(f"Segment ID '{segment_id}' has very long text ({len(paragraph_text)} chars). Truncating to {max_text_length} chars.")
            paragraph_text = paragraph_text[:max_text_length]

        success = False
        retry_count = 0

        while not success and retry_count <= max_retries:
            try:
                # If this is a retry, log it
                if retry_count > 0:
                    logger.info(f"Retry #{retry_count} for segment ID '{segment_id}'")

                retrieved_clips = phase2_retrieval.retrieve_results_for_query(
                    user_query_text=paragraph_text,
                    loaded_resources=retrieval_resources,
                    top_n_to_display=getattr(config, 'TOP_K_RESULTS', 10)
                )

                # Process the results
                for clip_info in retrieved_clips:
                    all_candidates.append({
                        'segment_id': segment_id,
                        'original_filepath': clip_info['clip_path'],
                        'similarity': clip_info.get('retrieval_score', 0.0),
                        'duration': clip_info.get('duration', 0.0), # Assumes 'duration' field from metadata_db
                        'raw_clip_info': clip_info
                    })

                logger.info(f"Segment ID '{segment_id}': Retrieved {len(retrieved_clips)} candidates.")
                success = True

            except Exception as e:
                retry_count += 1
                logger.error(f"Error retrieving results for segment ID '{segment_id}' (attempt {retry_count}/{max_retries+1}): {e}")

                if retry_count <= max_retries:
                    # Wait before retrying (exponential backoff)
                    import time
                    wait_time = 2 ** retry_count
                    logger.info(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                else:
                    # All retries failed
                    failed_segments.append(segment_id)
                    logger.error(f"All retries failed for segment ID '{segment_id}'. Moving to next segment.")
                    break

    # Report on failed segments
    if failed_segments:
        logger.warning(f"Failed to retrieve candidates for {len(failed_segments)} segments: {failed_segments}")

    logger.info(f"Total raw candidates retrieved: {len(all_candidates)}")
    return all_candidates

def assign_clips_globally(all_candidates):
    """全局分配逻辑：将同一个视频片段分配给相似度最高的 segment。"""
    candidates_by_filepath = defaultdict(list)
    for candidate in all_candidates:
        candidates_by_filepath[candidate['original_filepath']].append(candidate)

    best_assignment_for_clip = {}
    for filepath, candidates_for_file in candidates_by_filepath.items():
        candidates_for_file.sort(key=lambda x: (-x['similarity'], str(x['segment_id'])))
        if candidates_for_file:
            best_assignment_for_clip[filepath] = candidates_for_file[0]
    logger.info(f"Globally assigned {len(best_assignment_for_clip)} unique clips to segments.")
    return best_assignment_for_clip

def group_and_filter_assignments(best_assignment_for_clip, clip_count_per_segment):
    """将分配好的片段按 segment 分组，并按相似度排序，保留前 N 个。"""
    clips_assigned_to_segment = defaultdict(list)
    for _, assignment_details in best_assignment_for_clip.items():
        clips_assigned_to_segment[assignment_details['segment_id']].append(assignment_details)

    final_segment_assignments = {}
    for segment_id, assigned_clips in clips_assigned_to_segment.items():
        assigned_clips.sort(key=lambda x: x['similarity'], reverse=True)
        final_segment_assignments[segment_id] = assigned_clips[:clip_count_per_segment]
        logger.info(f"Segment ID '{segment_id}': Assigned {len(final_segment_assignments[segment_id])} clips after filtering (max {clip_count_per_segment}).")
    return final_segment_assignments

def prepare_output_directory(input_json_path, clips_subdir_name="CLIPS"):
    """准备输出目录，如果存在则清空。"""
    base_dir = os.path.dirname(os.path.abspath(input_json_path))
    clips_dir = os.path.join(base_dir, clips_subdir_name)
    try:
        if os.path.exists(clips_dir):
            logger.info(f"Clearing existing CLIPS directory: {clips_dir}")
            shutil.rmtree(clips_dir)
        os.makedirs(clips_dir, exist_ok=True)
        logger.info(f"Created CLIPS directory: {clips_dir}")
        return clips_dir
    except Exception as e:
        logger.error(f"Error preparing output directory {clips_dir}: {e}")
        return None

def copy_and_rename_clips_for_summary(final_segment_assignments, clips_dir):
    """复制并重命名视频文件，并准备用于 JSON 汇总的数据结构映射。"""
    processed_segments_summary_map = {}
    for segment_id_orig, assigned_clips in final_segment_assignments.items():
        segment_id_str = str(segment_id_orig)
        segment_entry_clips = []
        for rank, clip_data in enumerate(assigned_clips, 1):
            original_filepath = clip_data['original_filepath']
            _, original_extension = os.path.splitext(original_filepath)
            new_filename = f"{segment_id_str}_{rank}{original_extension}"
            destination_path = os.path.join(clips_dir, new_filename)
            try:
                if not os.path.exists(original_filepath):
                    logger.warning(f"Source file not found, skipping copy: {original_filepath}")
                    continue
                shutil.copy2(original_filepath, destination_path)
                logger.info(f"Copied '{original_filepath}' to '{destination_path}'")
                segment_entry_clips.append({
                    "filename": new_filename,
                    "duration": clip_data.get('duration', 0.0),
                    "similarity": clip_data['similarity']
                })
            except FileNotFoundError:
                 logger.error(f"File not found during copy: {original_filepath}. Skipping.")
            except Exception as e:
                logger.error(f"Error copying file '{original_filepath}' to '{destination_path}': {e}")

        if segment_entry_clips:
             processed_segments_summary_map[segment_id_str] = {
                "segment_id": segment_id_str,
                "assigned_clips": segment_entry_clips
            }
    return processed_segments_summary_map

def create_summary_json(summary_data_list, output_json_path):
    """创建 JSON 汇总文件。"""
    try:
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(summary_data_list, f, indent=2, ensure_ascii=False)
        logger.info(f"Successfully created summary JSON: {output_json_path}")
    except Exception as e:
        logger.error(f"Error creating summary JSON {output_json_path}: {e}")
# <<< END OF ADDED FUNCTIONS >>>

def main():
    # Set environment variables to prevent segmentation faults and memory issues

    # Disable tokenizer parallelism to prevent potential deadlocks/segmentation faults
    # when using tokenizers in loops or with multiprocessing.
    os.environ["TOKENIZERS_PARALLELISM"] = "false"

    # Add macOS-specific environment variables to prevent fork-related segmentation faults
    os.environ["OBJC_DISABLE_INITIALIZE_FORK_SAFETY"] = "YES"

    # Add PyTorch memory management settings
    os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"

    # Limit OpenMP threads which can cause issues with SentenceTransformer
    os.environ["OMP_NUM_THREADS"] = "1"

    # Limit BLAS threads
    os.environ["OPENBLAS_NUM_THREADS"] = "1"
    os.environ["MKL_NUM_THREADS"] = "1"

    # Force PyTorch to use CPU for SentenceTransformer operations
    os.environ["CUDA_VISIBLE_DEVICES"] = ""

    # Set memory management for transformers
    os.environ["TRANSFORMERS_OFFLINE"] = "1"  # Avoid network requests during model loading

    logger.info("Script starting...")
    parser = argparse.ArgumentParser(description="Process segments from a JSON file, retrieve video clips, assign them, and organize files.")
    parser.add_argument("--json", required=True, help="Path to the input JSON file containing segments.")
    args = parser.parse_args()

    logger.info(f"Using input JSON: {args.json}")
    logger.info(f"Target clips per segment (CLIP_COUNT): {CLIP_COUNT}")

    # 0. Ensure project directories (e.g., for FAISS indexes) exist if config provides a helper
    try:
        if hasattr(config, 'ensure_directories') and callable(config.ensure_directories):
            logger.info("Running config.ensure_directories()...")
            config.ensure_directories()
    except Exception as e:
        logger.warning(f"Could not run config.ensure_directories(): {e}. Proceeding cautiously.")

    # 1. Load input data
    segments_data = load_input_data(args.json)
    if not segments_data:
        logger.error("Failed to load input data. Exiting.")
        return

    # 2. Load retrieval resources (models, FAISS indexes, etc.)
    logger.info("Loading retrieval resources...")
    retrieval_resources = {}
    try:
        retrieval_resources = phase2_retrieval.load_resources_for_retrieval()
        # Check if critical resources are loaded
        intern_video_model_ok = retrieval_resources.get('intern_video_model') is not None
        sentence_model_ok = retrieval_resources.get('sentence_model') is not None
        visual_index = retrieval_resources.get('visual_index')
        text_index = retrieval_resources.get('text_index')
        visual_index_ok_and_not_empty = visual_index is not None and visual_index.ntotal > 0
        text_index_ok_and_not_empty = text_index is not None and text_index.ntotal > 0

        if not (intern_video_model_ok and sentence_model_ok and (visual_index_ok_and_not_empty or text_index_ok_and_not_empty)):
            logger.warning("One or more critical retrieval resources (models or a non-empty index) failed to load. Results may be limited or empty.")
        else:
            logger.info("Retrieval resources loaded successfully.")
            if visual_index_ok_and_not_empty:
                logger.info(f"Visual index loaded with {visual_index.ntotal} vectors.") # type: ignore
            if text_index_ok_and_not_empty:
                logger.info(f"Text index loaded with {text_index.ntotal} vectors.") # type: ignore

    except Exception as e:
        logger.error(f"Failed to load retrieval resources: {e}. Exiting.")
        # Optional: depending on severity, one might choose to sys.exit(1) here
        return

    # 3. Retrieve candidate clips for all segments
    all_candidates = retrieve_candidates_for_all_segments(segments_data, retrieval_resources)
    if not all_candidates:
        logger.warning("No candidates were retrieved for any segment. Proceeding to generate empty output structure.")

    # 4. Global assignment logic
    best_assignment_for_clip = assign_clips_globally(all_candidates)

    # 5. Group by assigned segment, filter by CLIP_COUNT, and sort
    final_segment_assignments = group_and_filter_assignments(best_assignment_for_clip, CLIP_COUNT)

    # 6. Prepare output directory (CLIPS subdir)
    clips_dir = prepare_output_directory(args.json)
    if not clips_dir:
        logger.error("Failed to prepare output directory. Exiting.")
        return

    # 7. Copy/rename files and get summary data for processed segments
    processed_segments_summary_map = copy_and_rename_clips_for_summary(final_segment_assignments, clips_dir)

    # 8. Construct the final list for the summary JSON, ensuring all original segments are included
    final_summary_list = []
    # Iterate through original segments_data to maintain order and include all segments
    for original_segment in segments_data:
        segment_id_str = str(original_segment['segment_id'])
        if segment_id_str in processed_segments_summary_map:
            final_summary_list.append(processed_segments_summary_map[segment_id_str])
        else:
            # This segment had no clips assigned or kept after filtering
            final_summary_list.append({
                "segment_id": segment_id_str,
                "assigned_clips": []
            })

    # 9. Create the summary JSON file
    summary_json_path = os.path.join(clips_dir, "assigned_clips.json")
    create_summary_json(final_summary_list, summary_json_path)

    logger.info("Script execution completed successfully.")
    logger.info(f"Output CLIPS directory: {clips_dir}")
    logger.info(f"Summary JSON file: {summary_json_path}")

if __name__ == "__main__":
    # Python path considerations for project structure:
    # If this script is in the project root alongside 'video_retrieval' (as a dir/package)
    # and 'config.py', direct imports should work.
    # If the structure is different (e.g., script in a 'scripts' subdir),
    # sys.path might need adjustment or the project should be installed as a package.
    # Example for adjusting sys.path if script is in a subdir:
    # current_dir = os.path.dirname(os.path.abspath(__file__))
    # project_root = os.path.dirname(current_dir)
    # if project_root not in sys.path:
    #     sys.path.insert(0, project_root)
    main()